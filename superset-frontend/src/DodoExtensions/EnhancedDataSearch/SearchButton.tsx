/* Search Morph Container */
.search-morph-container {
  width: 200px;
  display: flex;
  justify-content: flex-end;
  padding: 0;
}

.search-morph-wrapper {
  position: relative;
  width: 208px;
  height: 32px;
  display: flex;
  align-items: center;
  justify-content: center;
}

/* Search Morph Animation */
.search-morph {
  width: 28px;
  height: 28px;
  display: flex;
  align-items: center;
  box-sizing: border-box;
  position: absolute;
  left: 50%;
  transform: translateX(-50%);
  transition: all 0.15s;
  cursor: pointer;
}

.search-input {
  width: 100%;
  height: 100%;
  background: none;
  border: 1px solid #8a9199;
  border-radius: 20px;
  box-sizing: border-box;
  padding: 0 12px;
  font-size: 12px;
  color: transparent;
  z-index: 2;
  position: relative;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: flex-start;
  transition: color 0.3s ease;
}

.search-icon-container {
  width: 28px;
  height: 28px;
  position: absolute;
  top: 1px;
  right: 0;
  z-index: 1;
}

.search-morph.search-open .search-icon-container {
  cursor: pointer;
}

.search-icon-before,
.search-icon-after {
  content: '';
  width: 10px;
  height: 1px;
  background-color: #8a9199;
  border-radius: 1px;
  position: absolute;
  transform-origin: 100% 100%;
}

.search-icon-after {
  bottom: -4px;
  right: -4px;
  transform: rotate(45deg);
}

.search-icon-before {
  top: -4px;
  right: -4px;
  transform: rotate(-45deg);
  opacity: 0;
}

.search-morph,
.search-icon-container,
.search-icon-before,
.search-icon-after {
  animation-duration: 1.1s;
  animation-fill-mode: forwards;
}

.search-morph.search-opening {
  animation-name: searchExpand;
}

.search-morph.search-opening .search-icon-before {
  animation-name: beforeMagic;
}

.search-morph.search-opening .search-icon-after {
  animation-name: afterMagic;
}

.search-morph.search-closing,
.search-morph.search-closing .search-icon-container,
.search-morph.search-closing .search-icon-before,
.search-morph.search-closing .search-icon-after {
  animation-direction: reverse;
}

.search-morph.search-closing {
  animation-name: searchExpand;
}

.search-morph.search-closing .search-icon-before {
  animation-name: beforeMagic;
}

.search-morph.search-closing .search-icon-after {
  animation-name: afterMagic;
}

/* Final open state */
.search-morph.search-open {
  width: 200px;
  height: 28px;
  color: #8a9199;
}

.search-morph.search-opening .search-input {
  color: transparent;
  transition-delay: 0.5s;
}

.search-morph.search-open .search-input {
  color: #8a9199;
}

.search-morph.search-open .search-icon-container {
  z-index: 3;
}

.search-morph.search-open .search-icon-before {
  width: 15px;
  top: 7px;
  right: 11px;
  opacity: 1;
}

.search-morph.search-open .search-icon-after {
  width: 15px;
  bottom: 9px;
  right: 11px;
}

@keyframes afterMagic {
  0% {}

  10% {
    width: 18px;
    bottom: -8px;
    right: -8px;
  }

  15% {
    opacity: 1;
  }

  35% {
    width: 10px;
    bottom: -2px;
    right: -2px;
    opacity: 0;
  }

  25% {
    opacity: 0;
  }

  64% {
    opacity: 0;
  }

  65% {
    opacity: 1;
    width: 10px;
    bottom: -1px;
    right: -2px;
  }

  75% {
    width: 22px;
    bottom: 3px;
    right: 8px;
  }

  100% {
    width: 15px;
    bottom: 9px;
    right: 11px;
  }
}

@keyframes beforeMagic {
  0% {}

  40% {
    opacity: 0;
  }

  55% {
    opacity: 1;
    width: 10px;
    top: -3px;
    right: -2px;
  }

  65% {
    width: 22px;
    top: 5px;
    right: 8px;
  }

  80% {
    width: 15px;
    top: 7px;
    right: 11px;
  }

  100% {
    width: 15px;
    top: 7px;
    right: 11px;
    opacity: 1;
  }
}

@keyframes searchExpand {
  0% {
    color: transparent;
  }

  20% {
    width: 28px;
  }

  45% {
    width: 200px;
  }

  99% {
    color: transparent;
  }

  100% {
    width: 200px;
    color: #8a9199;
  }
}

import React, { useState } from 'react'
import './SearchMorph.css'

export interface SearchMorphProps {
  handleClick?: () => void
  placeholder?: string
  state?: 'open' | 'closed'
}

type SearchState = 'open' | 'closed' | 'opening' | 'closing'

const SearchMorph: React.FC<SearchMorphProps> = ({
  handleClick,
  placeholder = "Search...",
  state = 'closed'
}) => {
  const [searchState, setSearchState] = useState<SearchState>(state)

  const handleSearchClick = () => {
    if (searchState === 'closed') {
      setSearchState('opening')
      setTimeout(() => {
        setSearchState('open')
      }, 1100)
    } else if (searchState === 'open') {
      setSearchState('closing')
      setTimeout(() => {
        setSearchState('closed')
      }, 1100)
    }
  }

  return (
    <div className="search-morph-container">
      <div className="search-morph-wrapper">
        <div
          className={`
            search-morph
            ${searchState === 'opening' ? 'search-opening' : ''}
            ${searchState === 'open' ? 'search-open' : ''}
            ${searchState === 'closing' ? 'search-closing' : ''}
          `}
          onClick={handleClick}
        >
          <div className="search-input">
            {searchState === 'open' && placeholder}
          </div>

          <div className="search-icon-container">
            <div className="search-icon-before"></div>
            <div className="search-icon-after"></div>
          </div>
        </div>
      </div>
    </div>
  )
}

export default SearchMorph
