import { ErrorLevel, styled, SupersetClient, t } from '@superset-ui/core';
import { useState, useRef, useEffect, useCallback } from 'react';
import Icons from 'src/components/Icons';
import { Input } from 'src/components/Input';
import Loading from 'src/components/Loading';
import BasicErrorAlert from 'src/components/ErrorMessage/BasicErrorAlert';
import { Link, useLocation } from 'react-router-dom';
import SearchButton from './SearchButton';

const mockResponse: Response = {
  predictions: {
    dashboards: [
      {
        title: 'Dashboard 1',
        score: 0.9,
        url: '/dashboard/1',
      },
      {
        title: 'Dashboard 2',
        score: 0.8,
        url: '/dashboard/2',
      },
      {
        title: 'Dashboard 3',
        score: 0.7,
        url: '/dashboard/3',
      },
    ],
    meta: {
      query: 'test',
      retrieved: 10,
      top_k: 3,
    },
  },
};

interface Response {
  predictions: {
    dashboards: {
      title: string;
      score: number;
      url: string;
    }[];
    meta: {
      query: string;
      retrieved: number;
      top_k: number;
    };
  };
}

const WHITE_LIST_PAGES = [
  '/superset/welcome/',
  '/dashboard/list/',
  '/chart/list/',
  '/tablemodelview/list/',
];

const ErrorAlertWrapper = styled.div`
  margin-top: ${({ theme }) => theme.gridUnit * 4}px;
`;

// Стили для input в RightMenu
const SearchInput = styled(Input)`
  width: 90px;
`;

const BlurOverlay = styled.div<{ isVisible: boolean }>`
  position: fixed;
  top: 55px;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: ${({ theme }) => theme.colors.grayscale.dark2}4D;
  backdrop-filter: blur(4px);
  z-index: 1000;
  display: ${({ isVisible }) => (isVisible ? 'block' : 'none')};
`;

const SearchDrawer = styled.div<{ isVisible: boolean }>`
  position: fixed;
  top: 55px; /* Высота header */
  left: 0;
  right: 0;
  display: grid;
  grid-template-rows: ${({ isVisible }) => (isVisible ? '1fr' : '0fr')};
  transition: all 0.3s ease-in-out;
  background-color: ${({ theme }) => theme.colors.grayscale.light5};
  z-index: 1001;
  opacity: ${({ isVisible }) => (isVisible ? 1 : 0)};
  visibility: ${({ isVisible }) => (isVisible ? 'visible' : 'hidden')};
  transition: all 0.3s ease-in-out;
`;

const DrawerContent = styled.div`
  overflow: hidden;
  padding: ${({ theme }) => theme.gridUnit * 6}px;
  width: min(100%, 800px);
  margin: 0 auto;
`;

const DrawerSearchInput = styled(Input)`
  width: 100%;
  margin-bottom: ${({ theme }) => theme.gridUnit * 4}px;

  .ant-input {
    font-size: ${({ theme }) => theme.typography.sizes.l}px;
    padding: ${({ theme }) => theme.gridUnit * 3}px
      ${({ theme }) => theme.gridUnit * 4}px;
    border-radius: ${({ theme }) => theme.gridUnit * 2}px;
  }
`;

const SearchLoader = styled.div`
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: ${({ theme }) => theme.gridUnit * 8}px;
  gap: ${({ theme }) => theme.gridUnit}px;

  p {
    margin: 0;
    color: ${({ theme }) => theme.colors.grayscale.base};
    font-size: ${({ theme }) => theme.typography.sizes.s}px;
  }
`;

const ResultsContainer = styled.div`
  margin-top: ${({ theme }) => theme.gridUnit * 4}px;
`;

const ResultsHeader = styled.div`
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: ${({ theme }) => theme.gridUnit * 4}px;
  padding-bottom: ${({ theme }) => theme.gridUnit * 2}px;
  border-bottom: 1px solid ${({ theme }) => theme.colors.grayscale.light2};

  h3 {
    margin: 0;
    font-size: ${({ theme }) => theme.typography.sizes.l}px;
    font-weight: ${({ theme }) => theme.typography.weights.medium};
    color: ${({ theme }) => theme.colors.grayscale.dark2};
  }
`;

const QueryInfo = styled.div`
  display: flex;
  align-items: center;
  gap: ${({ theme }) => theme.gridUnit * 2}px;
  font-size: ${({ theme }) => theme.typography.sizes.s}px;
  color: ${({ theme }) => theme.colors.grayscale.base};

  span {
    padding: ${({ theme }) => theme.gridUnit / 2}px
      ${({ theme }) => theme.gridUnit}px;
    background-color: ${({ theme }) => theme.colors.grayscale.light4};
    border-radius: ${({ theme }) => theme.gridUnit / 2}px;
  }
`;

const ResultsList = styled.div`
  display: flex;
  flex-direction: column;
  gap: ${({ theme }) => theme.gridUnit * 3}px;
`;

const ResultItem = styled(Link)`
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: ${({ theme }) => theme.gridUnit * 4}px;
  border: 1px solid ${({ theme }) => theme.colors.grayscale.light2};
  border-radius: ${({ theme }) => theme.gridUnit * 2}px;
  text-decoration: none;
  transition: all 0.2s ease;

  &:hover {
    border-color: ${({ theme }) => theme.colors.primary.light2};
    box-shadow: 0 2px 8px ${({ theme }) => theme.colors.grayscale.dark2}1A; /* 10% opacity */
    transform: translateY(-1px);
    text-decoration: none;
  }

  &:active {
    transform: translateY(0);
  }
`;

const ResultContent = styled.div`
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: ${({ theme }) => theme.gridUnit}px;
`;

const ResultTitle = styled.h4`
  margin: 0;
  font-size: ${({ theme }) => theme.typography.sizes.m}px;
  font-weight: ${({ theme }) => theme.typography.weights.medium};
  color: ${({ theme }) => theme.colors.grayscale.dark2};
  line-height: 1.4;
`;

const ResultMeta = styled.div`
  display: flex;
  align-items: center;
  gap: ${({ theme }) => theme.gridUnit}px;
  font-size: ${({ theme }) => theme.typography.sizes.s}px;
  color: ${({ theme }) => theme.colors.grayscale.base};

  span:first-child {
    color: ${({ theme }) => theme.colors.primary.base};
    font-weight: ${({ theme }) => theme.typography.weights.medium};
  }
`;

const ScoreContainer = styled.div`
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: ${({ theme }) => theme.gridUnit}px;
  min-width: 80px;
`;

const ScoreValue = styled.div<{ value: number }>`
  font-size: ${({ theme }) => theme.typography.sizes.m}px;
  font-weight: ${({ theme }) => theme.typography.weights.bold};
  color: ${({ theme, value }) =>
    value >= 0.8
      ? theme.colors.success.base
      : value >= 0.6
        ? theme.colors.warning.base
        : theme.colors.error.base};
`;

const ScoreBar = styled.div<{ score: number }>`
  width: 60px;
  height: 4px;
  background-color: ${({ theme }) => theme.colors.grayscale.light3};
  border-radius: 2px;
  position: relative;
  overflow: hidden;

  &::after {
    content: '';
    position: absolute;
    left: 0;
    top: 0;
    height: 100%;
    width: ${({ score }) => Math.min(score * 100, 100)}%;
    background: linear-gradient(
      90deg,
      ${({ theme }) => theme.colors.error.base} 0%,
      ${({ theme }) => theme.colors.warning.base} 50%,
      ${({ theme }) => theme.colors.success.base} 100%
    );
    border-radius: 3px;
    transition: width 0.3s ease;
  }
`;

const ScoreLabel = styled.span`
  font-size: ${({ theme }) => theme.typography.sizes.xs}px;
  color: ${({ theme }) => theme.colors.grayscale.base};
  text-transform: uppercase;
  letter-spacing: 0.5px;
`;

const EnhancedDataSearch = () => {
  const [isDrawerOpen, setIsDrawerOpen] = useState(false);
  const [searchQuery, setSearchQuery] = useState('');
  const [response, setResponse] = useState<Response | null>(mockResponse);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<{
    level: ErrorLevel;
    title: string;
    body: string;
  } | null>(null);

  const inputRef = useRef<any>(null);
  const buttonSearchRef = useRef<HTMLButtonElement>(null);

  useEffect(() => {
    if (isDrawerOpen) {
      window.scrollTo({ top: 0, behavior: 'smooth' });
      document.body.style.overflow = 'hidden';

      if (inputRef.current) {
        inputRef.current.focus();
      }
    } else {
      document.body.style.overflow = 'auto';
    }
    return () => {
      document.body.style.overflow = 'auto';
    };
  }, [isDrawerOpen]);

  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (
        isDrawerOpen &&
        !(event.target as Element).closest('.search-drawer') &&
        !(event.target as Element).closest('.search-button')
      ) {
        setIsDrawerOpen(false);
      }
    };

    if (isDrawerOpen) {
      document.addEventListener('mousedown', handleClickOutside);
      return () =>
        document.removeEventListener('mousedown', handleClickOutside);
    }
    return undefined;
  }, [isDrawerOpen]);

  const handleSearch = useCallback(async () => {
    if (!searchQuery || searchQuery.length < 3) return;

    setLoading(true);
    setResponse(null);
    setError(null);

    SupersetClient.post({
      endpoint: '/api/v1/search',
      jsonPayload: {
        query: searchQuery,
        top_k: 3,
      },
    })
      .then(response => {
        setResponse(response.json.result as Response);
      })
      .catch(err => {
        const title =
          err.status >= 500
            ? t('Service temporarily unavailable.')
            : t('Unexpected error. Status: %s', err.status);

        setError({
          level: err.status >= 500 ? 'error' : 'warning',
          title,
          body: err.statusText,
        });
      })
      .finally(() => setLoading(false));
  }, [searchQuery]);

  useEffect(() => {
    if (searchQuery.length >= 3) {
      const timeoutId = setTimeout(handleSearch, 500);
      return () => clearTimeout(timeoutId);
    }

    return undefined;
  }, [searchQuery, handleSearch]);

  return (
    <>
      <SearchButton
        ref={buttonSearchRef}
        handleClick={() => setIsDrawerOpen(prev => !prev)}
        placeholder={t('Search')}
        state={isDrawerOpen ? 'closed' : 'open'}
      />

      <BlurOverlay
        isVisible={isDrawerOpen}
        onClick={() => setIsDrawerOpen(false)}
      />

      <SearchDrawer isVisible={isDrawerOpen} className="search-drawer">
        <DrawerContent>
          <DrawerSearchInput
            ref={inputRef}
            placeholder={t('Search for dashboards...')}
            prefix={<Icons.SearchOutlined iconSize="l" />}
            value={searchQuery}
            onChange={e => setSearchQuery(e.target.value)}
            allowClear
          />

          {loading && (
            <SearchLoader>
              <Loading position="inline-centered" />
              <p>{t('Searching...')}</p>
            </SearchLoader>
          )}

          {error && (
            <ErrorAlertWrapper>
              <BasicErrorAlert
                title={error.title}
                level={error.level}
                body={error.body}
              />
            </ErrorAlertWrapper>
          )}

          {response && (
            <ResultsContainer>
              <ResultsHeader>
                <h3>{t('Search Results')}</h3>
                <QueryInfo>
                  <span>
                    {t('Query')}: {response.predictions.meta.query}
                  </span>
                  <span>
                    {t('Found')}: {response.predictions.meta.retrieved}
                  </span>
                </QueryInfo>
              </ResultsHeader>

              <ResultsList>
                {response.predictions.dashboards.map(dashboard => (
                  <ResultItem
                    key={dashboard.url}
                    to={dashboard.url}
                    target="_blank"
                    onClick={() => setIsDrawerOpen(false)}
                  >
                    <ResultContent>
                      <ResultTitle>{dashboard.title}</ResultTitle>
                      <ResultMeta>
                        <span>{t('Dashboard')}</span>
                        <span>•</span>
                        <span>{t('Click to open')}</span>
                      </ResultMeta>
                    </ResultContent>

                    <ScoreContainer>
                      <ScoreValue value={dashboard.score}>
                        {(dashboard.score * 100).toFixed(0)}%
                      </ScoreValue>
                      <ScoreBar score={dashboard.score} />
                      <ScoreLabel>{t('Relevance')}</ScoreLabel>
                    </ScoreContainer>
                  </ResultItem>
                ))}
              </ResultsList>
            </ResultsContainer>
          )}
        </DrawerContent>
      </SearchDrawer>
    </>
  );
};

export default EnhancedDataSearch;
